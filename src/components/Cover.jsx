import { Box, Button, Typography } from "@mui/material"

function Cover() {
    return (
        <Box
            id='cover'
            sx={{
                minHeight: 'calc(100vh - 80px)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                padding: '2rem 1rem',
                mt: '1.2rem',
                background: 'radial-gradient(circle 560px at center,rgb(152, 85, 255) 0%, #2a0b33 30%, #000 100%)'
            }}>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'black',
                    border:'1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '2rem',
                    padding: '0.5rem 1rem'
                }}>
                <Typography sx={{ backgroundColor: '#9855ff', borderRadius: '0.6rem', padding: '0.2rem 0.4rem', color: 'black', fontSize: '0.7rem' }}>
                    News
                </Typography>
                <Typography sx={{ padding: '0.2rem 0.4rem', color: '#9855ff', fontSize: '1rem' }}>
                    Proud to Be a Top 3 Finalist at the Berkeley Haas VCIC!
                </Typography>
            </Box>

            <Typography variant="h1" 
                sx={{ 
                    mt: '8rem', mb: '2rem', lineHeight: 1.0, fontWeight: 500, fontSize: '5.4rem',
                    background: "linear-gradient(to bottom, #ffffff 55%,rgb(221, 147, 228) 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                }}>
                Build Data Confidence <br /> with AI in Seconds
            </Typography>

            <Typography variant="h5"
                sx={{
                    maxWidth: '500px',
                    lineHeight: 1.5,
                    color: '#ffffff99',
                    fontSize: '1.6rem'
                }}>
                Life is short. Don’t waste time and money on tedious data work. Let AI handle it.
            </Typography>

            <Button variant="outlined"
                sx={{
                    color: '#fff',
                    borderColor: '#fff',
                    borderRadius: '0.5rem',
                    fontSize: '1.1rem',
                    textTransform: 'none',
                    mt: '4rem',
                    mb: '8rem',
                    '&:hover': {
                        backgroundColor: '#6e44ff',
                        borderColor: '#6e44ff',
                    },
                }}
                onClick={() => {
                    document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' })
                }}
                >
                Explore More
            </Button>
        </Box>
    )
}

export default Cover