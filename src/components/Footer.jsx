import { Box, IconButton, Typography } from '@mui/material'
import TwitterIcon from "@mui/icons-material/X"
import LinkedInIcon from "@mui/icons-material/LinkedIn"

function Footer() {
    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center', mt: '2rem', 
            maxWidth: "lg", margin: "0 auto"
         }}>
            <Box sx={{ display: "flex", flexDirection: 'row', gap: 1, padding: "0 1.5rem", mt: '2rem' }}>
                <IconButton href="https://x.com/kx_daicus" target="_blank" sx={{ color: "gray"}}>
                    <TwitterIcon />
                </IconButton>
                <IconButton href="https://www.linkedin.com/company/daicus-technology/" target="_blank" sx={{ color: "gray"}}>
                    <LinkedInIcon />
                </IconButton>
            </Box>
            <Typography variant="subtitle1" sx={{ color: '#999', mb: '4rem', padding: "0 2rem" }}>
                © 2025 Daicus Technology, Inc. All Rights Reserved.
            </Typography>
        </Box>
    )
}

export default Footer