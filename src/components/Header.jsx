import logo_daicus from '../assets/logo_daicus.png'
import { List, Button, ListItem, ListItemButton, Box, Menu, MenuItem, Popper, Paper, Grow, ClickAwayListener } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { useState, useRef } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'

function Header() {
    const navigate = useNavigate();
    const location = useLocation();
    const [solutionsOpen, setSolutionsOpen] = useState(false);
    const solutionsRef = useRef(null);

    const handleScrollNavigation = (sectionId) => {
        if (location.pathname === '/') {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            navigate('/', { state: { scrollTo: sectionId } });
        }
    };

    const handleSolutionsToggle = () => {
        setSolutionsOpen((prevOpen) => !prevOpen);
    };

    const handleSolutionsClose = (event) => {
        if (solutionsRef.current && solutionsRef.current.contains(event.target)) {
            return;
        }
        setSolutionsOpen(false);
    };

    const handleSolutionClick = (path) => {
        navigate(path);
        setSolutionsOpen(false);
    };

    return (
        <header style={{ 
            position: 'fixed', top: 0, left: 0, right: 0, zIndex: 999, backgroundColor: 'rgba(0, 0, 0, 0.9)', 
            display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '1rem 4rem', maxWidth: '1150px', margin: '0 auto',
            }}>
            <div>
                <img src={logo_daicus} alt="DAICUS" style={{ height: '70px' }}/>
            </div>

            <nav style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', borderRadius: '2rem', padding: '0.1rem 1rem'}}>
                <List sx={{ display: 'flex', alignItems: 'center', listStyle: 'none', margin: 0, padding: 0 }}>
                    <ListItem>
                        <ListItemButton
                            component={Link}
                            to='/'
                            sx={{
                                borderRadius: '1rem', color: '#999', '&:hover': { color: '#fff' }
                            }}
                            onClick={() => handleScrollNavigation('cover')}
                        >
                            Home
                        </ListItemButton>
                    </ListItem>
                    <ListItem>
                        <ListItemButton 
                            sx={{ 
                                borderRadius: '1rem', color: '#999', '&:hover': {color: '#fff'} 
                            }}
                            onClick={() => handleScrollNavigation('products')}
                            >
                            Products
                        </ListItemButton>
                    </ListItem>
                    <ListItem>
                        <ListItemButton
                            ref={solutionsRef}
                            sx={{ 
                                borderRadius: '1rem', 
                                color: '#999', 
                                '&:hover': {color: '#fff'},
                                display: 'flex',
                                alignItems: 'center',
                            }}
                            onClick={handleSolutionsToggle}
                            aria-controls={solutionsOpen ? 'solutions-menu' : undefined}
                            aria-haspopup="true"
                            aria-expanded={solutionsOpen ? 'true' : undefined}
                        >
                            <Box sx={{ mr: '0.2rem', transform: solutionsOpen ? 'none' : 'rotate(-90deg)', transition: 'transform 0.2s' }}>
                                <ArrowDropDownIcon />
                            </Box>
                            Solutions
                        </ListItemButton>
                        <Popper
                            open={solutionsOpen}
                            anchorEl={solutionsRef.current}
                            role={undefined}
                            placement="bottom-start"
                            transition
                            disablePortal
                            style={{ zIndex: 1000 }}
                        >
                            {({ TransitionProps, placement }) => (
                                <Grow
                                    {...TransitionProps}
                                    style={{
                                        transformOrigin: 'top left'
                                    }}
                                >
                                    <Paper
                                        sx={{
                                            backgroundColor: 'rgba(30, 30, 30, 0.95)',
                                            borderRadius: '0.5rem',
                                            mt: 0.5,
                                            boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
                                        }}
                                    >
                                        <ClickAwayListener onClickAway={handleSolutionsClose}>
                                            <Box>
                                                <MenuItem 
                                                    onClick={() => handleSolutionClick('/solutionESG')}
                                                    sx={{ 
                                                        color: '#ccc',
                                                        '&:hover': { 
                                                            backgroundColor: 'rgba(110, 68, 255, 0.2)',
                                                            color: '#fff'
                                                        },
                                                        py: 1.5,
                                                        px: 2.5,
                                                        minWidth: '200px'
                                                    }}
                                                >
                                                    ESG Policy Compliance
                                                </MenuItem>
                                            </Box>
                                        </ClickAwayListener>
                                    </Paper>
                                </Grow>
                            )}
                        </Popper>
                    </ListItem>
                    <ListItem>
                        <ListItemButton
                            sx={{ 
                                borderRadius: '1rem', color: '#999', '&:hover': {color: '#fff'} 
                            }}
                            onClick={() => handleScrollNavigation('contact')}
                            >
                            Company
                        </ListItemButton>
                    </ListItem>
                    <ListItem>
                        <ListItemButton
                            component={Link}
                            to='/blog'
                            sx={{
                                borderRadius: '1rem', color: '#999', '&:hover': { color: '#fff' }
                            }}                        
                        >
                            Blogs
                        </ListItemButton>
                    </ListItem>
                </List>
            </nav>

            <div style={{ marginLeft: '1rem'}}>
                <Button
                    sx={{ 
                        backgroundColor: '#6e44ff', 
                        border: '1px solid #6e44ff', 
                        borderRadius: '0.5rem',
                        textTransform: 'none',
                        color: '#fff',
                        paddingLeft: 2,
                        paddingRight: 2,
                        cursor: 'pointer',
                        '&:hover': {
                            backgroundColor: '#9a64ff',
                            transform: 'scale(1.02)',
                        },
                    }}
                    onClick={() => handleScrollNavigation('contact')}
                    >
                        Free Trial
                </Button>
            </div>
        </header>
    )
}

export default Header
