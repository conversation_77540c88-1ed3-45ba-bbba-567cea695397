import { Box, Card, CardMedia, CardContent, Grid2, Typography } from '@mui/material'
import avatarPengDu from '../assets/testimonial_peng_du.jpeg'
import avatarPaulBryzek from '../assets/testimonial_paul_bryzek.png'

function Testimonials() {
    return (
        <Box id="testimonials" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', mb: '16rem', paddingTop: '120px' }}>
            <Typography sx={{ fontSize: '2.6rem', textAlign: 'center', lineHeight: 1.1 }}>
                Clients Who Inspire Us
            </Typography>
            <Typography variant='h5' sx={{ color: '#999', textAlign: 'center', mt: '2rem' }}>
                Hear from the real clients about how our AI solutions <br /> have boosted business success.
            </Typography>

            <Grid2 container spacing={4} justifyContent="center" sx={{ mt: '5rem' }}>
                <Grid2 item xs={12} sm={6} md={4}>
                    <Card sx={{ 
                        backgroundColor: 'transparent',
                        // border: '1px solid rgba(255, 255, 255, 0.2)',
                        // borderRadius: 2,
                        width: 500,
                        padding: '1rem 1rem' }}>
                        <CardMedia component="img" image={avatarPengDu} sx={{ height: 200, width: 'auto', borderRadius: 6, transform: 'scale(0.8)' }} />
                        <CardContent>
                            <Typography variant="h5" sx={{ color: 'white' }}>
                                "We turned our idea into a working service in just two months with Daicus's RAG reference architecture, debuting at the largest rallycross event in the US."
                            </Typography>
                            <Typography variant="subtitle1" sx={{ color: 'white', mt: '1.5rem' }}>
                                Peng Du, Ph.D.
                            </Typography>
                            <Typography variant="subtitle1" sx={{ color: '#999' }}>
                                Co-Founder and CEO @ Apexiel
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid2>
                <Grid2 item xs={12} sm={6} md={4}>
                    <Card sx={{ 
                        backgroundColor: 'transparent',
                        // border: '1px solid rgba(255, 255, 255, 0.2)',
                        // borderRadius: 2,
                        width: 500,
                        padding: '1rem 1rem' }}>
                        <CardMedia component="img" image={avatarPaulBryzek} sx={{ height: 200, width: 'auto', borderRadius: 6, transform: 'scale(0.8)' }} />
                        <CardContent>
                            <Typography variant="h5" sx={{ color: 'white' }}>
                                "Daicus provided us with a robust and secure AI-agent powered on premise deployment for data extraction and ESG policy automation."
                            </Typography>
                            <Typography variant="subtitle1" sx={{ color: 'white', mt: '1.5rem' }}>
                                Paul Bryzek, MBA
                            </Typography>
                            <Typography variant="subtitle1" sx={{ color: '#999' }}>
                                Founder and CEO @ Carbon Sustain
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid2>
            </Grid2>
        </Box>
    )
}

export default Testimonials