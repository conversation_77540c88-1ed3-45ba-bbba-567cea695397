import { Box, Card, CardContent, CardMedia, Grid2, Typography } from '@mui/material'
import ColorizeOutlinedIcon from '@mui/icons-material/ColorizeOutlined'
import ChecklistOutlinedIcon from '@mui/icons-material/ChecklistOutlined'
import HistoryEduOutlinedIcon from '@mui/icons-material/HistoryEduOutlined'

function Products() {
    return (
        <Box id="products" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', paddingTop: '120px', paddingBottom: '12rem', 
            background: 'radial-gradient(circle 600px at center, #2E0249 0%, #320A88 0%, #000 100%)',
         }}>
            <Typography sx={{ fontSize: '2.6rem', textAlign: 'center', lineHeight: 1.1 }}>
                Unlock Agentic AI automation to extract, <br />
                analyze, and verify business data effortlessly.
            </Typography>

            <Grid2 container spacing={4} justifyContent="center" sx={{ mt: '5rem' }}>
                <Grid2 item xs={12} sm={6} md={4}>
                    <Card sx={{ 
                        backgroundColor: 'transparent',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        borderRadius: 2,
                        width: 320,
                        padding: '1rem 1rem' }}>
                        <CardMedia component={ColorizeOutlinedIcon} sx={{ height: 300, width: 'auto', color: '#999', transform: 'scale(0.5)' }} />
                        <CardContent>
                            <Typography variant="h5" sx={{ fontWeight: 600, color: 'white' }}>
                                Data Extraction
                            </Typography>
                            <Typography variant="body1" sx={{ color: '#aaa', mt: '2rem' }}>
                                Extract data with accuracy and speed from PDFs, images, and more.
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid2>

                <Grid2 item xs={12} sm={6} md={4}>
                    <Card sx={{ 
                        backgroundColor: 'transparent',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        borderRadius: 2,
                        width: 320,
                        padding: '1rem 1rem' }}>
                        <CardMedia component={ChecklistOutlinedIcon} sx={{ height: 300, width: 'auto', color: '#999', transform: 'scale(0.5)' }} />
                        <CardContent>
                            <Typography variant="h5" sx={{ fontWeight: 600, color: 'white' }}>
                                Data Verification
                            </Typography>
                            <Typography variant="body1" sx={{ color: '#aaa', mt: '2rem' }}>
                                Verify data with consistency for compliance, audits, and more.
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid2>

                <Grid2 item xs={12} sm={6} md={4}>
                    <Card sx={{ 
                        backgroundColor: 'transparent',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        borderRadius: 2,
                        width: 320,
                        padding: '1rem 1rem' }}>
                        <CardMedia component={HistoryEduOutlinedIcon} sx={{ height: 300, width: 'auto', color: '#999', transform: 'scale(0.5)' }} />
                        <CardContent>
                            <Typography variant="h5" sx={{ fontWeight: 600, color: 'white' }}>
                                Smart Report
                            </Typography>
                            <Typography variant="body1" sx={{ color: '#aaa', mt: '2rem' }}>
                                Generate on-demand reports intelligently using data.
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid2>
            </Grid2>
        </Box>


    )
}

export default Products