import { Box, Button, Typography } from '@mui/material'

function Contact() {
    return (
        <Box id="contact" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', mb: '6rem',
            height: "50vh",
            // background: 'radial-gradient(circle 600px at top, #2E0249 0%, #320A88 0%, #000 100%)',
            background: 'radial-gradient(circle 600px at top,rgb(79, 10, 128) 0%,rgb(83, 8, 148) 0%, #000 100%)',
         }}>
            <Typography sx={{ fontSize: '2.6rem', textAlign: 'center', lineHeight: 1.1 }}>
                Use Future Data Tools Now.
            </Typography>

            <Button variant="outlined"
                href="mailto:<EMAIL>"
                sx={{
                    color: '#fff',
                    borderColor: '#fff',
                    borderRadius: '0.5rem',
                    fontSize: '1.1rem',
                    textTransform: 'none',
                    mt: '4rem',
                    '&:hover': {
                        backgroundColor: '#6e44ff',
                        borderColor: '#6e44ff',
                    },
                }}>
                Talk to Us
            </Button>

            <Typography variant="h5" sx={{ color: '#999', textAlign: 'center', mt: '1.5rem' }}>
                Spend your precious time with those that matter, <br /> instead of manual data work.
            </Typography>
        </Box>
    )
}

export default Contact