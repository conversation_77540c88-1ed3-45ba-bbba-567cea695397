import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>po<PERSON>, Button, Grid2, useMediaQuery } from '@mui/material'
import { createTheme } from '@mui/material/styles'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// ADD IMPORTS HERE:
import blog20250214 from '../assets/BlogFiles/20250214_blog.md' 
import blog20250208 from '../assets/BlogFiles/20250208_blog.md' 
import blog20241012 from '../assets/BlogFiles/20241012_blog.md' 
import preview20250214 from '../assets/BlogFiles/20250214_blog_preview.png'
import preview20250208 from '../assets/BlogFiles/20250208_blog_preview.png'
import preview20241012 from '../assets/BlogFiles/20241012_blog_preview.png'

// TO ADD A BLOG POST:
// Import the FILE NAME and IMAGE similar to the current imports
// Add a blog post to 'blogPosts' similar to current blog blogPosts
// Currently, you need: title, fileName, imageSrc
const blogPosts = [
    // { cardTitle: 'Sample Blog Post', fileName: textBlog, imageSrc: testBlogPic },
    { cardTitle: 'Reflections as a Top 3 Finalist at Berkeley VCIC', fileName: blog20250214, imageSrc: preview20250214 },
    { cardTitle: 'Thoughts as a Founder from the Entrepreneurship Panel', fileName: blog20250208, imageSrc: preview20250208 },
    { cardTitle: 'Daicus Joins Microsoft for Startups Founders Hub', fileName: blog20241012, imageSrc: preview20241012 },
]

// Extract title, author, and content from .md raw text
function parseMarkdown(rawText) {
    const lines = rawText.split('\n');
    
    var title = "";
    var author = "";
    var date = "";
    const contentLines = [];
    
    for (const line of lines) {
        if (line.startsWith("title: ")) {
            title = line.substring(7).trim()
        } else if (line.startsWith("author: ")) {
            author = line.substring(8).trim();
        } else if (line.startsWith("date: ")) {
            date = line.substring(6).trim();
        } else {
            contentLines.push(line);
        }
    }
    return {
        title,
        author,
        date,
        content: contentLines.join('\n'),
    };
}

// Splits the blogs into columns (for different screen widths)
function chunkArray(array, size) {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
        result.push(array.slice(i, i + size));
    }
    return result;
}

// Creates a blog card
function BlogCard({ cardIndex, cardTitle, fileName, imageSrc, onExpand, isSelected }) {
    const handleClick = () => {
        onExpand(cardIndex, fileName);
    };

    return (
        <Grid2 item xs={12} sm={6} md={4} >
            <Box
                onClick={handleClick}
                sx={{
                    position: 'relative',
                    overflow: 'visible',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    backgroundColor: isSelected ? 'rgba(85, 19, 103, 0.4)' : 'rgba(68, 17, 86, 0.3)',
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16,
                    borderBottomLeftRadius: isSelected ? 0 : 16,
                    borderBottomRightRadius: isSelected ? 0 : 16,
                    borderBottom: isSelected ? 0 : '1px solid rgba(255, 255, 255, 0.2)',
                    boxShadow: '0 8px 16px rgba(18, 1, 19, 0.9)',
                    padding: isSelected ? '2rem 1.2rem 4rem 1.2rem' : '2rem 1.2rem 2rem 1.2rem',
                    minWidth: 260,
                    maxWidth: 260,
                    height: 'auto',
                    cursor: 'pointer',
                    transition: 'transform 0.3s ease',
                    '&:hover': {
                        backgroundColor: 'rgba(85, 19, 103, 0.4)',
                        transform: 'scale(1.02)',
                    },

                }}
            >
                <Box
                    component="img"
                    src={imageSrc}
                    alt="Blog Card"
                    sx={{
                        width: '100%',
                        aspectRatio: '1 / 1',
                        objectFit: 'cover',
                        borderRadius: 1,
                    }}
                />
                <Typography
                    variant="h6"
                    sx={{
                        height: '3.5rem',
                        color: '#bbb',
                        mt: 2,
                        mb: 0,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2, // limit to 2 lines
                        WebkitBoxOrient: 'vertical',
                    }}>
                    {cardTitle}
                </Typography>
            </Box>
        </Grid2>
    );
}

// check if the image in markdown is lanscape
function ResponsiveImage({ src, alt }) {
    const [isLandscape, setIsLandscape] = useState(null)
  
    useEffect(() => {
      const img = new window.Image()
      img.src = src
      img.onload = () => {
        setIsLandscape(img.naturalWidth >= img.naturalHeight)
      }
    }, [src])
  
    if (isLandscape === null) return null
  
    return (
      <Box
        component="img"
        src={src}
        alt={alt}
        sx={{
          width: isLandscape ? '80%' : '50%',
          maxWidth: '100%',
          borderRadius: '8px',
          display: 'block',
          mt: 6,
          mb: 1,
        }}
      />
    )
  }

// Makes the main aspect of the blog page
function Blogs() {
    // scroll up to the top when entering this page
    useEffect(() => { window.scrollTo({ top: 0, behavior: 'smooth' }) }, []) 

    // blogsPerPage constant determines number of blogs per page
    const [currentPage, setCurrentPage] = useState(0);
    const blogsPerPage = 6; 

    // Which card is expanded
    const [expandedIndex, setExpandedIndex] = useState(null);

    // Data from the .md file
    const [expandedTitle, setExpandedTitle] = useState('');
    const [expandedAuthor, setExpandedAuthor] = useState('');
    const [expandedDate, setExpandedDate] = useState('');
    const [expandedContent, setExpandedContent] = useState('');

    // columns based on screen size
    const theme = createTheme({
        breakpoints: {
            values: {
                xs: 0,
                sm: 745,
                md: 1220,
            },
        },
    });
    const isMd = useMediaQuery(theme.breakpoints.up('md'));
    const isSm = useMediaQuery(theme.breakpoints.up('sm'));
    let columns = 1;
    if (isMd) {
        columns = 3;
    } else if (isSm) {
        columns = 2;
    }

    // Pagination calculations
    const totalPages = Math.ceil(blogPosts.length / blogsPerPage);
    const startIndex = currentPage * blogsPerPage;
    const endIndex = startIndex + blogsPerPage;
    const currentBlogs = blogPosts.slice(startIndex, endIndex);

    // Reset expanded blog when page changes
    useEffect(() => {
        setExpandedIndex(null);
        setExpandedTitle('');
        setExpandedAuthor('');
        setExpandedDate('');
        setExpandedContent('');
    }, [currentPage]);

    // Expands the selected blog card
    const handleExpand = async (cardIndex, fileName) => {
        if (expandedIndex === cardIndex) {
            // close
            setExpandedIndex(null);
            setExpandedTitle('');
            setExpandedAuthor('');
            setExpandedDate('');
            setExpandedContent('');
            return;
        }

        // expand
        try {
            const resp = await fetch(fileName);
            const rawText = await resp.text();
            const { title, author, date, content } = parseMarkdown(rawText);

            setExpandedTitle(title);
            setExpandedAuthor(author);
            setExpandedDate(date);
            setExpandedContent(content);
            setExpandedIndex(cardIndex);
        } catch (err) {
            console.error('Error fetching blog:', err);
            setExpandedContent('Could not load blog content.');
            setExpandedIndex(cardIndex);
        }
    };

    // Closes the expanded blog card
    const handleClose = () => {
        setExpandedIndex(null);
        setExpandedTitle('');
        setExpandedAuthor('');
        setExpandedDate('');
        setExpandedContent('');
    };

    const rows = chunkArray(currentBlogs, columns);

    // Pagination/Navigation styling
    const paginationButtonStyle = {
        minWidth: '40px',
        height: '40px',
        color: '#bbb',
        border: '0px solid rgba(110, 68, 255, 0.4)',
        borderRadius: '8px',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        fontSize: '1rem',
        fontWeight: 100,
        padding: '0 12px',
        backgroundColor: 'rgba(110, 68, 255, 0.1)',
        '&:hover': {
            backgroundColor: 'rgba(110, 68, 255, 0.3)',
            boxShadow: '0 2px 8px rgba(110, 68, 255, 0.2)',
            transform: 'translateY(-1px)'
        },
        '&:disabled': {
            color: 'rgba(255, 255, 255, 0.3)',
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            cursor: 'not-allowed',
            transform: 'none',
            boxShadow: 'none'
        },
        '&:numDisabled': {
            color: 'rgba(255, 255, 255, 0.3)',
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            cursor: 'not-allowed',
            transform: 'none',
            boxShadow: 'none'
        },
        '&.MuiButton-root': {
            fontFamily: '"Inter", sans-serif',
            textTransform: 'none',
            letterSpacing: '0.025em'
    }
    };

    // Pagination controls
    const renderPagination = () => (
        <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            mt: 4,
            gap: 1,
            flexWrap: 'wrap',
            position: 'relative',
            zIndex: 1,
        }}>
            <Button
                onClick={() => setCurrentPage(0)}
                disabled={currentPage === 0}
                sx={paginationButtonStyle}
                aria-label="First page"
            >
                First
            </Button>
            <Button
                onClick={() => setCurrentPage(p => Math.max(p - 1, 0))}
                disabled={currentPage === 0}
                sx={paginationButtonStyle}
                aria-label="Previous page"
            >
                Prev
            </Button>

            <Box sx={{
                    display: 'flex',
                    gap: 1,
                    mx: 1,
                    '& .MuiButton-root': {
                        minWidth: '48px',
                        padding: '0 8px'
                    }
                }}>
                {Array.from({ length: totalPages }, (_, index) => (
                    <Button
                        key={index}
                        onClick={() => setCurrentPage(index)}
                        numDisabled={currentPage === index}
                        sx={{
                            ...paginationButtonStyle,
                            backgroundColor: currentPage === index ? 'rgba(110, 68, 255, 0.7)' : 'rgba(110, 68, 255, 0.1)',
                            '&:hover': currentPage !== index && { backgroundColor: 'rgba(110, 68, 255, 0.5)'}
                        }}
                        aria-label={`Page ${index + 1}`}
                    >
                        {index + 1}
                    </Button>
                ))}
            </Box>

            <Button
                onClick={() => setCurrentPage(p => Math.min(p + 1, totalPages - 1))}
                disabled={currentPage >= totalPages - 1}
                sx={paginationButtonStyle}
                aria-label="Next page"
            >
                Next
            </Button>
            <Button
                onClick={() => setCurrentPage(totalPages - 1)}
                disabled={currentPage >= totalPages - 1}
                sx={paginationButtonStyle}
                aria-label="Last page"
            >
                Last
            </Button>
        </Box>
    );

    return (
        <Box
            sx={{
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.3)), \
                                repeating-linear-gradient(110deg, rgba(0, 0, 0, 0.9), rgba(137, 24, 219, 0.09), rgba(0, 0, 0, 0.9)), \
                                repeating-linear-gradient(70deg, rgba(17, 67, 204, 0.1), rgba(72, 19, 185, 0.15), rgba(165, 12, 225, 0.1))`,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                backgroundAttachment: 'fixed',
                pt: '120px',
                pb: '12rem',
                px: '3rem',
            }}
        >
            <Typography sx={{ fontSize: '2.6rem', textAlign: 'center', lineHeight: 1.1, mb: '8rem', mt: '5rem' }}>
                Our Insights and Actions <br /> That Shape the Future
            </Typography>

            {renderPagination()}

            {rows.map((rowItems, rowIndex) => {
                const rowElement = (
                    <Grid2
                        container
                        key={`row-${rowIndex}`}
                        spacing={6}
                        justifyContent="center"
                        alignItems="stretch"
                        size={{ xs: 12, sm: 6, md: 4 }}
                        sx={{
                            margin: '0 auto',
                            mb: '0rem',
                            mt: '2rem',
                            mx: '1.5rem',
                        }}
                    >
                        {rowItems.map((blog, colIndex) => {
                            const cardIndex = rowIndex * columns + colIndex;
                            const isSelected = expandedIndex === cardIndex
                            return (
                                <BlogCard
                                    key={`card-${cardIndex}`}
                                    cardIndex={cardIndex}
                                    cardTitle={blog.cardTitle}
                                    fileName={blog.fileName}
                                    imageSrc={blog.imageSrc}
                                    onExpand={handleExpand}
                                    isSelected={isSelected}
                                />
                            );
                        })}
                    </Grid2>
                );

                let expandedRow = null;
                const cardIndexesInThisRow = rowItems.map((_, i) => rowIndex * columns + i);
                if (cardIndexesInThisRow.includes(expandedIndex)) {
                    expandedRow = (
                        <Grid2
                            container
                            key={`expanded-${expandedIndex}`}
                            justifyContent="center"
                            alignItems="center"
                            sx={{
                                width: '90%',
                                maxWidth: '1500px',
                                mx: 'auto',
                                mt: '0rem',
                                mb: '2rem',
                            }}
                        >
                            <Grid2 item>
                                <Box
                                    sx={{
                                        backgroundColor: 'rgba(85, 20, 103, 0.3)',
                                        border: '1px solid rgba(255, 255, 255, 0.3)',
                                        borderRadius: 4,
                                        padding: 4,
                                        color: '#aaa',
                                        position: 'relative',
                                        fontFamily: "sans-serif",
                                        fontSize: 24,
                                    }}
                                >
                                    <Button
                                        variant="contained"
                                        size="small"
                                        sx={{
                                            top: '1rem',
                                            right: '1rem',
                                            backgroundColor: '#6e44ff',
                                            border: '0px solid #6e44ff',
                                            borderRadius: '0.5rem',
                                            position: 'absolute',
                                            color: '#bbb',
                                            cursor: 'pointer',
                                            '&:hover': {
                                                backgroundColor: 'rgb(135, 78, 242)',
                                                transform: 'scale(1.02)',
                                            },
                                        }}
                                        onClick={handleClose}
                                    >
                                        Close
                                    </Button>

                                    <Typography variant="h3" sx={{ fontSize: '1.5rem', lineHeight: 1.3, color: '#ddd', mb: 1 }}>
                                        {expandedTitle}
                                    </Typography>
                                    <Typography variant="subtitle2" sx={{ fontSize: '1rem', lineHeight: 1.4, color: '#ccc', mb: 0 }}>
                                        By: {expandedAuthor} 
                                    </Typography>
                                    <Typography variant="subtitle2" sx={{ fontSize: '1rem', lineHeight: 1.4, color: '#ccc', mb: 6 }}>
                                        Date: {expandedDate}
                                    </Typography>

                                    <ReactMarkdown
                                        rehypePlugins={[remarkGfm]}

                                        components={{
                                            // paragragh
                                            p: ({ children }) => (
                                                <Typography variant='body1' sx={{ fontSize: '1rem', lineHeight: 1.6, color: '#ddd', mb: 2 }}>
                                                    {children}
                                                </Typography>
                                            ),
                                            // list
                                            li: ({ children }) => (
                                                <Typography component='li' variant='body1' sx={{ fontSize: '1rem', lineHeight: 1.6, color: '#ddd', mb: 1 }}>
                                                    {children}
                                                </Typography>
                                            ),
                                            // inline code & block code
                                            code: ({ node, inline, className, children, ...props }) => {
                                            if (inline) {
                                                return (
                                                <Box
                                                    component="code"
                                                    sx={{
                                                    fontFamily: 'monospace',
                                                    backgroundColor: 'rgba(255,255,255,0.1)',
                                                    px:0.5, py:0.2,
                                                    borderRadius:'4px',
                                                    fontSize:'0.875rem',
                                                    }}
                                                    {...props}
                                                >
                                                    {children}
                                                </Box>
                                                )
                                            }
                                            // pre code block
                                            return (
                                                <Box
                                                component="pre"
                                                sx={{
                                                    fontFamily: 'monospace',
                                                    backgroundColor: 'rgba(255,255,255,0.05)',
                                                    p:2,
                                                    fontSize:'0.875rem',
                                                    borderRadius:'6px',
                                                    overflowX:'auto',
                                                    mb:2,
                                                }}
                                                >
                                                <Box component="code" className={className} sx={{ display:'block' }} {...props}>
                                                    {children}
                                                </Box>
                                                </Box>
                                            )
                                            },
                                            // table  
                                            table: ({ children }) => (
                                                <Box component="table" sx={{ width:'100%', borderCollapse:'collapse', mb:2 }}>
                                                  {children}
                                                </Box>
                                            ),
                                            th: ({ children }) => (
                                                <Box component="th" sx={{ textAlign:'left', borderBottom:'1px solid rgba(255,255,255,0.3)', py:1, pr:2, fontWeight:500 }}>
                                                  {children}
                                                </Box>
                                            ),
                                            td: ({ children }) => (
                                                <Box component="td" sx={{ py:1, pr:2 }}>
                                                  {children}
                                                </Box>
                                            ),
                                            blockquote: ({ children }) => (
                                                <Typography
                                                  component="blockquote"
                                                  variant="body2"
                                                  sx={{
                                                    borderLeft: '4px solid rgba(110, 68, 255, 0.7)',  // 左侧高亮条
                                                    pl: 2,    // 左内边距
                                                    ml: 0,    // 去掉默认缩进
                                                    my: 2,    // 上下外边距
                                                    color: '#666',           // 更深的灰色
                                                    fontSize: '0.875rem',    // 14px
                                                    lineHeight: 1.5,
                                                    fontStyle: 'italic',
                                                    backgroundColor: 'rgba(255,255,255,0.05)', // 可选底色
                                                    borderRadius: '4px',
                                                  }}
                                                >
                                                  {children}
                                                </Typography>
                                            ),
                                            // Renders pictures
                                            img: ({ src, alt }) => {
                                                try {
                                                    const resolvedSrc = require(`../assets/BlogFiles/${src}`)
                                                    return <ResponsiveImage src={resolvedSrc} alt={alt} />
                                                } catch (error) {
                                                    console.error("⚠️ Image not found:", src)
                                                    return <Typography color="error">⚠️ Image not found: {src}</Typography>
                                                }
                                            },
                                            // Makes links brighter and more readable
                                            a: ({ children, ...props }) => (
                                                <a 
                                                    {...props} 
                                                    style={{ 
                                                    color: '#6e44ff', 
                                                    textDecoration: 'none',
                                                    fontWeight: 500,
                                                    borderBottom: '1px solid rgba(152, 143, 255, 0.3)',
                                                    transition: 'all 0.2s ease',
                                                    }}
                                                > {children}
                                                </a>
                                                )
                                            }}
                                    >{expandedContent}</ReactMarkdown>
                                </Box>
                            </Grid2>
                        </Grid2>
                    );
                }

                return (
                    <React.Fragment key={`fragment-${rowIndex}`}>
                        {rowElement}
                        {expandedRow}
                    </React.Fragment>
                );
            })}

            {renderPagination()}
        </Box>
    );
}

export default Blogs;