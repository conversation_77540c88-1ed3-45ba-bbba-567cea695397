import { Box, Card, CardMedia, Typography } from '@mui/material'
import logoBerkeleyHaas from '../assets/logo_berkeley_haas.png'
import logoCarbonSustain from '../assets/logo_carbonsustain.png'
import logoApexiel from '../assets/logo_apexiel.png'

function Trustor() {
    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', mt: '4rem', mb: '12rem' }}>
            <Typography variant='h5' sx={{ color: '#999' }}>
                Trusted by
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center', mt: '4rem', gap: '2rem' }}>
                <Card sx={{ 
                    backgroundColor: 'transparent',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: 2,
                    width: 200,
                    padding: '0rem 2rem' }}>
                    <CardMedia component="img" image={logoCarbonSustain} sx={{ objectFit: 'contain', height: 70 }} />
                </Card>
                <Card sx={{ 
                    backgroundColor: 'transparent',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: 2,
                    width: 200,
                    padding: '0rem 2rem' }}>
                    <CardMedia component="img" image={logoBerkeleyHaas} sx={{ objectFit: 'contain', height: 70, transform: 'scale(0.75)' }} />
                </Card>
                <Card sx={{ 
                    backgroundColor: 'transparent',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: 2,
                    width: 200,
                    padding: '0rem 2rem' }}>
                    <CardMedia component="img" image={logoApexiel} sx={{ objectFit: 'contain', height: 70, transform: 'scale(0.75)' }} />
                </Card>
            </Box>
        </Box>
    )
}

export default Trustor