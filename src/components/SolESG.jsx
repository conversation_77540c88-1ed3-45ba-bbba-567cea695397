import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, Card, Avatar } from '@mui/material'
import { Description, CloudUpload, Edit, Send, CheckCircle, Translate } from '@mui/icons-material'
import React from 'react'
import carbon_sustain_logo2 from '../assets/logo_carbonsustain2.png'
import apexiel_logo from '../assets/logo_apexiel.png'
import berkeley_haas_logo from '../assets/logo_berkeley_haas.png'
import carbon_sustain_logo from '../assets/logo_carbonsustain.png'
import ESG_demo_video from '../assets/ESG_demo_video.mp4'

function SolutionESG() {

		{/* --- Pain Point in Industry RADAR CONSTS --- */}
		const radarPoints = [
				{ angle: 270.00, value: 1.0, label: 'Manual review' },
				{ angle: 321.43, value: 0.9, label: 'Inconsistent' },
				{ angle: 12.857, value: 0.8, label: 'Wasted time' },
				{ angle: 64.286, value: 0.7, label: 'Slow' },
				{ angle: 115.71, value: 0.7, label: 'Repetitive reviews' },
				{ angle: 167.14, value: 0.6, label: 'Unreliable' },
				{ angle: 218.57, value: 0.5, label: 'Tedious' },
		];

		const RadarChart = () => {
				const centerX = 150;
				const centerY = 150;
				const maxRadius = 120;

				// Render the radar circles
				const gridCircles = [0.2, 0.4, 0.6, 0.8, 1.0].map(scale => (
						<circle 
								key={scale}
								cx={centerX}
								cy={centerY}
								r={maxRadius * scale}
								fill='none'
								stroke='#555'
								strokeWidth='1'
								opacity='0.8'
						/>
				));

				// Render the chart axes
				const axes = radarPoints.map((point, index) => {
						const angle = (point.angle * Math.PI) / 180;
						const x2 = centerX + maxRadius * Math.cos(angle);
						const y2 = centerY + maxRadius * Math.sin(angle);

						return (
								<line
										key={index}
										x1={centerX}
										y1={centerY}
										x2={x2}
										y2={y2}
										stroke='#555'
										strokeWidth='1'
										opacity='0.8'
								/>
						);
				});
				
				// Render the data polygon
				const polygonPoints = radarPoints.map(point => {
						const angle = (point.angle * Math.PI) / 180;
						const radius = maxRadius * point.value;
						const x = centerX + radius * Math.cos(angle);
						const y = centerY + radius * Math.sin(angle);
						return `${x},${y}`;
				}).join(' ');

				// Render the labels
				const labels = radarPoints.map((point, index) => {
						const angle = (point.angle * Math.PI) / 180;
						const labelRadius = maxRadius + 50 * point.value;
						const x = centerX + labelRadius * Math.cos(angle) * 1.1;
						const y = centerY + labelRadius * Math.sin(angle) * 0.85 + 8;
						return (
								<text
										key={index}
										x={x}
										y={y}
										textAnchor='middle'
										dominantBaseline='middle'
										fontSize='12'
										fontWeight='550'
										fill='#eee'
										fontFamily='Roboto, sans-serif'
								>
										{point.label}
								</text>
						);
				});
				
				return (
						<svg
								minWidth='320'
								width='100%'
								height='340'
								viewBox='0 0 300 300'
						>
								{gridCircles}
								{axes}
								<polygon
										points={polygonPoints}
										fill='#6e44ff'
										fillOpacity='0.4'
										stroke='#6e44ff'
										strokeWidth='2'
								/>
								{labels}
						</svg>
				)
		}

		const workflowSteps = [
				{
						icon: <Description />,
						title: 'Select documents',
						subtitle: 'you want to cross check.'
				},
				{
						icon: <CloudUpload />,
						title: 'Upload both',
						subtitle: 'target and reference data.'
				},
				{
						icon: <Edit />,
						title: 'Write',
						subtitle: 'a simple query.'
				},
				{
						icon: <Send />,
						title: 'Submit',
						subtitle: 'the query.'
				},
				{
						icon: <CheckCircle />,
						title: 'Receive',
						subtitle: 'results.'
				},
		];

		const keyBenefits = [
				'Instantly detect ESG policy gaps',
				'No manual review needed',
				'Audit-ready, clear reports',
				'Private, secure on-premise processing',
				'No cloud dependency required'
		]

		const statsData = [
				{
						change: '90%',
						description: 'Reduction in manual ESG review time.'
				},
				{
						change: '3-5x',
						description: 'Faster reporting cycles.'
				},
				{
						change: '99%+',
						description: 'Accuracy on AI-extracted and flagged ESG information.'
				},
				{
						change: '100%',
						description: 'Data privacy with secure on-premise AI deployment.'
				}
		]

		const trusters = {
				'apexiel_logo' 				: apexiel_logo,
				'berkeley_haas_logo' 	: berkeley_haas_logo,
				'carbon_sustain_logo'	: carbon_sustain_logo
		}

	  return (
				<Box
						sx={{
								display: 'flex',
								flexDirection: 'column',
								marginTop: '6rem',
								alignItems: 'center',
								justifyContent: 'center',
								py: 'clamp(2rem, 6vw, 4rem)',
								px: 'clamp(1rem, 8vw, 8rem)',
						}}
				>
						<Typography
								variant='h1'
								sx={{
									  fontSize: 'clamp(1.8rem, 6vw, 4rem)',
										fontWeight: '520',
										textAlign: 'center',
										lineHeight: 1.1,
								}}
						>
								AI Solutions for ESG Teams
						</Typography>

						<Typography
								variant='h4'
								sx={{
										mt: 'clamp(1rem, 3vw, 1.6rem)',
									  fontSize: 'clamp(0.9rem, 2.5vw, 1.2rem)',
										fontWeight: '500',
										textAlign: 'center',
										lineHeight: 1.1,
										color: '#ccc'
								}}
						>
								Automate policy checks and document extraction with AI.
						</Typography>

						<Button
								sx={{
										alignItems: 'center',
										backgroundColor: '#6e44ff',
										border: '1px solid #6e44ff',
										borderRadius: 'clamp(0.8rem, 2vw, 1.2rem)',
										fontSize: 'clamp(0.9rem, 2vw, 1rem)',
										color: '#fff',
										mt: 'clamp(1.5rem, 4vw, 2rem)',
										px: 'clamp(1.5em, 4vw, 2em)',
										py: 'clamp(0.4rem, 1vw, 0.5rem)',
										cursor: 'pointer',
										'&:hover': {
												backgroundColor: '#9a64ff',
												transform: 'scale(1.02)',
										},
								}}
						>
								Request a Demo
						</Button>

						<Typography
								sx={{
										mt: 'clamp(1.5rem, 4vw, 2rem)',
									  fontSize: 'clamp(1.2rem, 3vw, 1.6rem)',
										fontWeight: '500',
										fontStyle: 'italic',
										textAlign: 'center',
										lineHeight: 1.1,
								}}
						>
								ESG Policy Compliance Check
						</Typography>
				
						{/* --- Pain Point in Industry RADAR --- */}
						<Box sx={{
							textAlign: 'center',
							mt: 'clamp(1.5rem, 4vw, 2rem)',
							transform: 'scale(clamp(0.7, 0.0005vw + 0.65, 1))'
						}}>
								<Card
										sx={{
												minWidth: '520px',
												mx: 'auto',
												p: 'clamp(1rem, 4vw, 2rem)',
												backgroundColor: '#161616',
												borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
												boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
												transform: 'scale(clamp(0.7, 0.0005vw + 0.65, 1))'
										}}
								>
										<Typography variant='h6' sx={{ mb: 'clamp(0.5rem, 2vw, 1rem)', fontWeight: 600, color: '#fff', fontSize: 'clamp(1rem, 2.5vw, 1.25rem)' }}>
												Pain Point in the Industry
										</Typography>
										<Box sx={{ display: 'flex', justifyContent: 'center' }}>
												<RadarChart />
										</Box>
								</Card>
						</Box>

						{/* --- How it Works --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: 'clamp(1.5rem, 4vw, 2rem)'
								}}
						>
								<Card
										sx={{
												p: 'clamp(1.2rem, 4vw, 2rem)',
												backgroundColor: '#161616',
												borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
										}}
								>
										<Typography
												variant='h4'
												sx={{
														fontWeight: 600,
														mb: 'clamp(0.5rem, 2vw, 1rem)',
														color: '#eee',
														fontSize: 'clamp(1.5rem, 4vw, 2.125rem)'
												}}
										>
												How it works
										</Typography>
										<Box
												sx={{
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														flexWrap: 'wrap',
														gap: 'clamp(1rem, 3vw, 1.5rem)',
														px: 'clamp(0.5rem, 3vw, 2rem)'
												}}
										>
												{workflowSteps.map((step, index) => (
														<React.Fragment key={index}>
																<Box
																		sx={{
																				textAlign: 'center',
																				minWidth: 'clamp(100px, 20vw, 120px)',
																				flex: '1 1 auto'
																		}}
																>
																		<Avatar
																				sx={{
																						backgroundColor: '#2d1b5e',
																						width: 'clamp(50px, 10vw, 60px)',
																						height: 'clamp(50px, 10vw, 60px)',
																						mx: 'auto',
																						mb: 'clamp(0.5rem, 2vw, 1rem)'
																				}}
																		>
																				{step.icon}
																		</Avatar>
																		<Typography
																				variant='body2'
																				sx={{
																						fontWeight: 600,
																						color: '#eee',
																						mb: 'clamp(0.25rem, 1vw, 0.5rem)',
																						fontSize: 'clamp(0.8rem, 2vw, 0.875rem)'
																				}}
																		>
																				{step.title}
																		</Typography>
																		<Typography
																				variant='body2'
																				sx={{
																						fontSize: 'clamp(0.7rem, 1.8vw, 0.9rem)',
																						color: '#777'
																				}}
																		>
																				{step.subtitle}
																		</Typography>
																</Box>
																{index < workflowSteps.length - 1 && (
																		<Typography
																				sx={{
																						color: '#6e44ff',
																						fontSize: 'clamp(1rem, 3vw, 1.5rem)',
																						mx: 'clamp(0.5rem, 1vw, 1rem)',
																						position: 'relative',
																						top: 'clamp(-20px, -5vw, -32px)',
																						display: { xs: 'none', sm: 'block' }
																				}}
																		>
																				→
																		</Typography>
																)}
														</React.Fragment>
												))}
										</Box>
								</Card>
						</Box>

						{/* --- Key Benefits --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: 'clamp(1.5rem, 4vw, 2rem)',
								}}
						>
								<Card
										sx={{
												py: 'clamp(1.5rem, 4vw, 2rem)',
												px: 'clamp(1rem, 8vw, 6rem)',
												backgroundColor: '#161616',
												borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
										}}
								>
										<Typography
												variant='h5'
												sx={{
														mb: 'clamp(0.5rem, 2vw, 1rem)',
														fontWeight: 600,
														color: '#eee',
														fontSize: 'clamp(1.2rem, 3vw, 1.5rem)'
												}}
										>
												KEY BENEFITS
										</Typography>

										<Box
												sx={{
														display: 'flex',
														flexDirection: 'column',
														alignItems: 'flex-start',
														maxWidth: 'min(400px, 90vw)',
														mx: 'auto'
												}}
										>
												{keyBenefits.map((benefit, index) => (
														<Box
																key={index}
																sx={{
																		display: 'flex',
																		alignItems: 'center',
																		mb: 'clamp(0.75rem, 2vw, 1rem)',
																		position: 'relative',
																		width: '100%'
																}}
														>
																<Box
																		sx={{
																				width: 'clamp(3px, 1vw, 4px)',
																				height: 'clamp(30px, 6vw, 40px)',
																				backgroundColor: '#6e44ff',
																				borderRadius: '2px',
																				mr: 'clamp(0.75rem, 2vw, 1rem)',
																				flexShrink: 0
																		}}
																/>
																<Typography
																		sx={{
																				color: '#eee',
																				fontSize: 'clamp(0.9rem, 2.2vw, 1rem)',
																				textAlign: 'left'
																		}}
																>
																		{benefit}
																</Typography>
														</Box>
												))}
										</Box>
								</Card>
						</Box>

						{/* --- Benefits in Numbers --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: 'clamp(1.5rem, 4vw, 2rem)',
										p: 'clamp(1rem, 4vw, 2rem)',
										pb: 'clamp(2rem, 6vw, 4rem)',
										backgroundColor: '#161616',
										borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
										boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
								}}
						>
								<Typography
										variant='h5'
										sx={{
												fontWeight: 600,
												mb: 'clamp(1rem, 4vw, 2rem)',
												color: '#eee',
												fontSize: 'clamp(1.2rem, 3vw, 1.5rem)'
										}}
								>
										Benefits in Numbers
								</Typography>

								<Box
										sx={{
												display: 'grid',
												gridTemplateColumns: {
													xs: '1fr',
													sm: 'repeat(2, 1fr)',
													md: 'repeat(4, 1fr)'
												},
												gap: 'clamp(1rem, 3vw, 2rem)',
										}}
								>
										{statsData.map((stat, index) => (
												<Card
														key={index}
														sx={{
																pt: 'clamp(1rem, 3vw, 1.5rem)',
																px: 'clamp(0.75rem, 2vw, 1rem)',
																pb: 'clamp(0.5rem, 1vw, 0.5rem)',
																textAlign: 'center',
																backgroundColor: '#24023d',
																borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
																boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
																height: '100%',
																display: 'flex',
																flexDirection: 'column',
														}}
												>
														<Typography
																variant='h3'
																sx={{
																		color: '#7e55ff',
																		fontWeight: 700,
																		mb: 2,
																		fontSize: 'clamp(1.8rem, 4vw, 2.5rem)'
																}}
														>
																{stat.change}
														</Typography>
														<Typography
																variant='body2'
																sx={{
																		color: '#eee',
																		fontWeight: 520,
																		lineHeight: 1.4,
																		fontSize: 'clamp(0.8rem, 3vw, 1rem)'
																}}
														>
																{stat.description}
														</Typography>
												</Card>
										))}
								</Box>
						</Box>

						{/* --- Carbon Sustain --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: 'clamp(1.5rem, 4vw, 2rem)'
								}}
						>
								<Card
										sx={{
												p: 'clamp(1rem, 4vw, 2rem)',
												backgroundColor: '#161616',
												borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
												maxWidth: 'min(800px, 95vw)',
												mx: 'auto'
										}}
								>
										<Box
												sx={{
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														mb: 'clamp(0.5rem, 2vw, 1rem)'
												}}
										>
												<Typography
														variant='h6'
														sx={{
																color: '#4caf50',
																fontWeight: 600,
																display: 'flex',
																alignItems: 'center',
																gap: 1
														}}
												>
														<img
																src={carbon_sustain_logo2}
																alt="Carbon Sustain Logo"
																style={{
																	width: 'clamp(150px, 30vw, 200px)',
																	height: 'auto',
																	maxHeight: '36px'
																}}
														/>
												</Typography>
										</Box>

										<Typography
												variant='body1'
												sx={{
														mb: 'clamp(0.5rem, 2vw, 1rem)',
														fontStyle: 'italic',
														fontSize: 'clamp(1rem, 2.5vw, 1.2rem)',
														color: '#eee',
														lineHeight: 1.6
												}}
										>
												"Daicus provided us with a robust and secure AI-agent powered on premise
												deployment for data extraction and ESG policy automation."
										</Typography>

										<Typography
												variant='body2'
												sx={{
														color: '#777',
														fontSize: 'clamp(0.8rem, 2vw, 0.875rem)'
												}}
										>
												Jane Smith, CEO of Carbon Sustain
										</Typography>
								</Card>
						</Box>

						{/* --- Trusted By --- */}
						<Box
								sx={{
										display: 'flex',
										flexWrap: 'wrap',
										justifyContent: 'center',
										gap: 'clamp(0.5rem, 2vw, 1rem)',
										mt: 'clamp(1.5rem, 4vw, 2rem)'
								}}
						>
								{Object.keys(trusters).map((truster, index) => (
										<Box
												key={index}
												sx={{
														backgroundColor: '#6e44ff',
														borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
														width: 'clamp(200px, 30vw, 15rem)',
														height: 'clamp(2.5rem, 6vw, 3.2rem)',
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														px: 'clamp(0.5rem, 2vw, 1rem)'
												}}
										>
												<Typography
														sx={{
															fontSize: 'clamp(0.7rem, 1.8vw, 0.875rem)',
															display: 'flex',
															alignItems: 'center',
															gap: 'clamp(0.25rem, 1vw, 0.5rem)'
														}}
												>
														Trusted by
														<img
																src={trusters[truster]}
																alt="Truster Logo"
																style={{
																	width: 'clamp(60px, 15vw, 100px)',
																	height: 'auto',
																	transform: 'translateY(2px)'
																}}
														/>
												</Typography>
										</Box>
								))}
						</Box>

						{/* --- Demo Video --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: 'clamp(1.5rem, 4vw, 2rem)',
										mx: 'auto',
										p: 'clamp(0.75rem, 4vw, 2rem)',
										maxWidth: 'min(900px, 95vw)',
										backgroundColor: '#161616',
										borderRadius: 'clamp(0.5rem, 2vw, 1rem)',
										boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
								}}
						>
								<Typography
										variant='h5'
										sx={{
												mb: 'clamp(1rem, 4vw, 4rem)',
												fontWeight: 600,
												fontSize: 'clamp(1rem, 3vw, 1.5rem)',
												lineHeight: 1.2,
												px: 'clamp(0.5rem, 2vw, 1rem)'
										}}
								>
										Full Tour Due Diligence Demo Video
								</Typography>
								<Box
										sx={{
												position: 'relative',
												width: '100%',
												aspectRation: '16/9',
												overflow: 'hidden',
												borderRadius: 'clamp(0.25rem, 1vw, 0.5rem)',
										}}
								>
										<video
												width="100%"
												height="auto"
												controls
												playsInline
												style={{
														top: 0,
														left: 0,
														width: '100%',
														height: '100%',
														objectFit: 'contain',
														borderRadius: 'clamp(0.25rem, 1vw, 0.5rem)'
												}}
										>
												<source src={ESG_demo_video} type="video/mp4" />
										</video>
								</Box>
						</Box>
				</Box>
		)
}

export default SolutionESG
