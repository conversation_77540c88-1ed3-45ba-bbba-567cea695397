import { <PERSON>, Typo<PERSON>, Button, Card, Avatar, Grid } from '@mui/material'
import { Description, CloudUpload, Edit, Send, CheckCircle, Translate } from '@mui/icons-material'
import React from 'react'
import carbon_sustain_logo2 from '../assets/logo_carbonsustain2.png'
import apexiel_logo from '../assets/logo_apexiel.png'
import berkeley_haas_logo from '../assets/logo_berkeley_haas.png'
import carbon_sustain_logo from '../assets/logo_carbonsustain.png'
import ESG_demo_video from '../assets/ESG_demo_video.mp4'

function SolutionESG() {

		{/* --- Pain Point in Industry RADAR CONSTS --- */}
		const radarPoints = [
				{ angle: 270.00, value: 1.0, label: 'Manual review' },
				{ angle: 321.43, value: 0.9, label: 'Inconsistent' },
				{ angle: 12.857, value: 0.8, label: 'Wasted time' },
				{ angle: 64.286, value: 0.7, label: 'Slow' },
				{ angle: 115.71, value: 0.7, label: 'Repetitive reviews' },
				{ angle: 167.14, value: 0.6, label: 'Unreliable' },
				{ angle: 218.57, value: 0.5, label: 'Tedious' },
		];

		const RadarChart = () => {
				const centerX = 150;
				const centerY = 150;
				const maxRadius = 120;

				// Render the radar circles
				const gridCircles = [0.2, 0.4, 0.6, 0.8, 1.0].map(scale => (
						<circle 
								key={scale}
								cx={centerX}
								cy={centerY}
								r={maxRadius * scale}
								fill='none'
								stroke='#555'
								strokeWidth='1'
								opacity='0.8'
						/>
				));

				// Render the chart axes
				const axes = radarPoints.map((point, index) => {
						const angle = (point.angle * Math.PI) / 180;
						const x2 = centerX + maxRadius * Math.cos(angle);
						const y2 = centerY + maxRadius * Math.sin(angle);

						return (
								<line
										key={index}
										x1={centerX}
										y1={centerY}
										x2={x2}
										y2={y2}
										stroke='#555'
										strokeWidth='1'
										opacity='0.8'
								/>
						);
				});
				
				// Render the data polygon
				const polygonPoints = radarPoints.map(point => {
						const angle = (point.angle * Math.PI) / 180;
						const radius = maxRadius * point.value;
						const x = centerX + radius * Math.cos(angle);
						const y = centerY + radius * Math.sin(angle);
						return `${x},${y}`;
				}).join(' ');

				// Render the labels
				const labels = radarPoints.map((point, index) => {
						const angle = (point.angle * Math.PI) / 180;
						const labelRadius = maxRadius + 50 * point.value;
						const x = centerX + labelRadius * Math.cos(angle) * 1.1;
						const y = centerY + labelRadius * Math.sin(angle) * 0.85 + 8;
						return (
								<text
										key={index}
										x={x}
										y={y}
										textAnchor='middle'
										dominantBaseline='middle'
										fontSize='12'
										fontWeight='550'
										fill='#eee'
										fontFamily='Roboto, sans-serif'
								>
										{point.label}
								</text>
						);
				});
				
				return (
						<svg minWidth='320' width='100%' height='340' viewBox='0 0 300 300'>
								{gridCircles}
								{axes}
								<polygon
										points={polygonPoints}
										fill='#6e44ff'
										fillOpacity='0.4'
										stroke='#6e44ff'
										strokeWidth='2'
								/>
								{labels}
						</svg>
				)
		}

		const workflowSteps = [
				{
						icon: <Description />,
						title: 'Select documents',
						subtitle: 'you want to cross check.'
				},
				{
						icon: <CloudUpload />,
						title: 'Upload both',
						subtitle: 'target and reference data.'
				},
				{
						icon: <Edit />,
						title: 'Write',
						subtitle: 'a simple query.'
				},
				{
						icon: <Send />,
						title: 'Submit',
						subtitle: 'the query.'
				},
				{
						icon: <CheckCircle />,
						title: 'Receive',
						subtitle: 'results.'
				},
		];

		const keyBenefits = [
				'Instantly detect ESG policy gaps',
				'No manual review needed',
				'Audit-ready, clear reports',
				'Private, secure on-premise processing',
				'No cloud dependency required'
		]

		const statsData = [
				{
						change: '90%',
						description: 'Reduction in manual ESG review time.'
				},
				{
						change: '3-5x',
						description: 'Faster reporting cycles.'
				},
				{
						change: '99%+',
						description: 'Accuracy on AI-extracted and flagged ESG information.'
				},
				{
						change: '100%',
						description: 'Data privacy with secure on-premise AI deployment.'
				}
		]

		const trusters = {
				'apexiel_logo' 				: apexiel_logo,
				'berkeley_haas_logo' 	: berkeley_haas_logo,
				'carbon_sustain_logo'	: carbon_sustain_logo
		}

	  return (
				<Box
						sx={{
								display: 'flex',
								flexDirection: 'column',
								marginTop: '6rem',
								alignItems: 'center',
								justifyContent: 'center',
								py: '4rem',
								px: '8rem'
						}}
				>
						<Typography 
								variant='h1'
								sx={{
									  fontSize: '4rem',
										fontWeight: '520',
										textAlign: 'center',
										lineHeight: 1.1,
								}}
						>
								AI Solutions for ESG Teams
						</Typography>

						<Typography 
								variant='h4'
								sx={{
										mt: '1.6rem',
									  fontSize: '1.2rem',
										fontWeight: '500',
										textAlign: 'center',
										lineHeight: 1.1,
										color: '#ccc'
								}}
						>
								Automate policy checks and document extraction with AI.
						</Typography>

						<Button
								sx={{ 
										alignItems: 'center',
										backgroundColor: '#6e44ff', 
										border: '1px solid #6e44ff', 
										borderRadius: '1.2rem',
										fontSize: '1rem',
										color: '#fff',
										mt: '2rem',
										px: '2em',
										py: '0.5rem',
										cursor: 'pointer',
										'&:hover': {
												backgroundColor: '#9a64ff',
												transform: 'scale(1.02)',
										},
							}}
						>
								Request a Demo
						</Button>

						<Typography 
								sx={{
										mt: '2rem',
									  fontSize: '1.6rem',
										fontWeight: '500',
										fontStyle: 'italic',
										textAlign: 'center',
										lineHeight: 1.1,
								}}
						>
								ESG Policy Compliance Check
						</Typography>
				
						{/* --- Pain Point in Industry RADAR --- */}
						<Box sx={{ textAlign: 'center', mt: '2rem' }}>
								<Card
										sx={{
												minWidth: '520px',
												mx: 'auto',
												p: '2rem',
												backgroundColor: '#161616',
												borderRadius: '1rem',
												boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
										}}
								>
										<Typography variant='h6' sx={{ mb: '1rem', fontWeight: 600,color: '#fff' }}>
												Pain Point in the Industry
										</Typography>
										<Box sx={{ display: 'flex', justifyContent: 'center' }}>
												<RadarChart />
										</Box>
								</Card>
						</Box>

						{/* --- How it Works --- */}
						<Box
								sx={{
										textAlign: 'center', 
										mt: '2rem'
								}}
						>	
								<Card
										sx={{
												p: '2rem',
												backgroundColor: '#161616',
												borderRadius: '1rem',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
										}}
								>
										<Typography 
												variant='h4' 
												sx={{ 
														fontWeight: 600,
														mb: '1rem',
														color: '#eee'
												}}
										>
												How it works
										</Typography>
										<Box
												sx={{
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														flexWrap: 'wrap',
														gap: 2.5,
														px: '2rem'
												}}
										>
												{workflowSteps.map((step, index) => (
														<React.Fragment key={index}>
																<Box 
																		sx={{
																				textAlign: 'center',
																				minWidth: '120px'
																		}}
																>
																		<Avatar
																				sx={{
																						backgroundColor: '#2d1b5e',
																						width: '60px',
																						height: '60px',
																						mx: 'auto',
																						mb: '1rem'
																				}}
																		>
																				{step.icon}
																		</Avatar>
																		<Typography
																				variant='body2'
																				sx={{
																						fontWeight: 600,
																						color: '#eee',
																						mb: '0.5rem'
																				}}
																		>
																				{step.title}
																		</Typography>
																		<Typography
																				variant='body2'
																				sx={{
																						fontSize: '0.9rem',
																						color: '#777'
																				}}
																		>
																				{step.subtitle}
																		</Typography>
																</Box>
																{index < workflowSteps.length - 1 && (
																		<Typography 
																				sx={{
																						color: '#6e44ff',
																						fontSize: '1.5rem',
																						mx: 1,
																						position: 'relative',
																						top: '-32px'
																				}}
																		>
																				→
																		</Typography>
																)}
														</React.Fragment>
												))}
										</Box>
								</Card>
						</Box>

						{/* --- Key Benefits --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: '2rem',
								}}
						>
								<Card
										sx={{
												py: '2rem',
												px: '6rem',
												backgroundColor: '#161616',
												borderRadius: '1rem',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
										}}
								>
										<Typography
												variant='h5'
												sx={{
														mb: '1rem',
														fontWeight: 600,
														color: '#eee'
												}}
										>
												KEY BENEFITS
										</Typography>
										
										<Box
												sx={{
														display: 'flex',
														flexDirection: 'column',
														alignItems: 'flex-start',
														maxWidth: 400,
														mx: 'auto'
												}}
										>
												{keyBenefits.map((benefit, index) => (
														<Box 
																key={index}
																sx={{
																		display: 'flex',
																		alignItems: 'center',
																		mb: '1rem',
																		position: 'relative'
																}}
														>
																<Box
																		sx={{
																				width: '4px',
																				height: '40px',
																				backgroundColor: '#6e44ff',
																				borderRadius: '2px',
																				mr: '1rem',
																		}}
																/>
																<Typography 
																		sx={{ 
																				color: '#eee', 
																				fontSize: '1rem' 
																		}}
																>
																		{benefit}
																</Typography>
														</Box>
												))}
										</Box>		
								</Card>
						</Box>

						{/* --- Benefits in Numbers --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: '2rem',
										p: '2rem',
										pb: '4rem',
										backgroundColor: '#161616',
										borderRadius: '1rem',
										boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
								}}
						>
								<Typography
										variant='h5'
										sx={{
												fontWeight: 600,
												mb: '2rem',
												color: '#eee'
										}}
								>
										Benefits in Numbers
								</Typography>

								<Grid
										container
										spacing={4}
										sx={{
												
										}}
								>
										{statsData.map((stat, index) => (
												<Grid
														item
														xs={12}
														sm={6}
														md={3}
														key={index}
												>
														<Card
																sx={{
																		pt: '1.5rem',
																		px: '1rem',
																		pb: '0.5rem',
																		textAlign: 'center',
																		backgroundColor: '#24023d',
																		borderRadius: '1rem',
																		boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
																		height: '100%'
																}}
														>
																<Typography
																		variant='h3'
																		sx={{
																				color: '#6e44ff',
																				fontWeight: 700,
																				mb: 2,
																				fontSize: '2.5rem'
																		}}
																>
																		{stat.change}
																</Typography>
																<Typography
																		variant='body2'
																		sx={{
																				color: '#eee',
																				fontWeight: 520,
																				lineHeight: 1.4
																		}}
																>
																		{stat.description}
																</Typography>
														</Card>
												</Grid>
										))}
								</Grid>
						</Box>

						{/* --- Carbon Sustain --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: '2rem'
								}}
						>
								<Card
										sx={{
												p: '2rem',
												backgroundColor: '#161616',
												borderRadius: '1rem',
												boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
												maxWidth: 800,
												mx: 'auto'
										}}
								>
										<Box
												sx={{
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														mb: '1rem'
												}}
										>
												<Typography
														variant='h6'
														sx={{
																color: '#4caf50',
																fontWeight: 600,
																display: 'flex',
																alignItems: 'center',
																gap: 1
														}}
												>
														<img src={carbon_sustain_logo2} alt="Carbon Sustain Logo" style={{width: '200px', height: '36px' }}/>
												</Typography>
										</Box>

										<Typography
												variant='body1'
												sx={{
														mb: '1rem',
														fontStyle: 'italic',
														fontSize: '1.2rem',
														color: '#eee',
														lineHeight: 1.6
												}}
										>
												"Daicus provided us with a robust and secure AI-agent powered on premise
												deployment for data extraction and ESG policy automation."
										</Typography>

										<Typography
												variant='body2'
												sx={{
														color: '#777'
												}}
										>
												Jane Smith, CEO of Carbon Sustain
										</Typography>
								</Card>
						</Box>

						{/* --- Trusted By --- */}
						<Box
								sx={{
										display: 'flex',
										justifyContent: 'center',
										gap: 2,
										mt: '2rem'
								}}
						>
								{Object.keys(trusters).map((truster) => (
										<Box
												sx={{
														backgroundColor: '#6e44ff',
														borderRadius: '1rem',
														width: '15rem',
														height: '3.2rem',
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center'
												}}
										>
												<Typography>
														Trusted by  <img src={trusters[truster]} alt="Truster Logo" style={{ width: '100px', height: 'auto', transform: 'translate(2px, 6px)' }}/>
												</Typography>
										</Box>
								))}
						</Box>

						{/* --- Demo Video --- */}
						<Box
								sx={{
										textAlign: 'center',
										mt: '2rem',
										mx: 'auto',
										p: '2rem',
										maxWidth: '900px',
										backgroundColor: '#161616',
										borderRadius: '1rem',
										boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',

								}}
						>
								<Typography
										variant='h5'
										sx={{
												mb: 4,
												fontWeight: 600
										}}
								>
										Full Tour Due Diligence Demo Video
								</Typography>
								
								<video width="900" height="auto" controls autoplay>
										<source src={ESG_demo_video} type="video/mp4" />
								</video>
						</Box>
				</Box>
		)
}

export default SolutionESG
