import React from 'react'

import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import '../App.css'
import Header from '../components/Header'
import Cover from '../components/Cover'
import Trustor from '../components/Trustor'
import Products from '../components/Products'
import Testimonials from '../components/Testimonials'
import Contact from '../components/Contact'
import Footer from '../components/Footer'

function Home() {
    const location = useLocation();

    useEffect(() => {
        if (location.state?.scrollTo) {
            const element = document.getElementById(location.state.scrollTo);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                // Clear the scroll state after scrolling
                window.history.replaceState({}, document.title);
            }
        }
    }, [location.state]);

    return (
        <div>
            <Header />
            <Cover />
            <Trustor />
            <Products />
            <Testimonials />
            <Contact />
            <Footer />
        </div>
    )
}

export default Home