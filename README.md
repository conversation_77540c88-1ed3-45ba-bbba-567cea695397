# Deployment Guide for daicus.com

## Deploy REACT Frontend
### Clone the repo
```
git clone https://github.com/kx-daicus/daicus_com_v2.git
```

### Install packages and virtual environment
```
sudo apt update
sudo apt install -y npm

cd daicus_com_v2
npm install
```

### Create a new branch for development
```
git checkout -b <your_branch_name>
```

### Start the frontend server
```
npm start
```

## Domain Setup
### Install nginx and certbot
```
sudo apt update
sudo apt install -y nginx
sudo apt install certbot python3-certbot-nginx -y
```

### Configure domain name
Configure the domain host name with the instance IP address. (This step must be done by <PERSON> <PERSON>.)

### Configure HTTPS certificates
```
sudo certbot -i nginx -d <domain_host_name>
```
Here, we use `www.daicus.com` as `domain_host_name`

Answer the question following the certbot instructions.
- Select 1 for the authentication.
- Enter email address: <EMAIL>.
- Type 'Y' for the aggreement.
- Type 'N' for email promotion.

Type the domain host name in a broswer and test if the welcome page can open.

If the certificate needs to be renewed, run the following commands under root for renewal.
```
sudo certbot renew --nginx
sudo certbot renew --dry-run # for testing renewal
```

### Configure nginx server
```
sudo vim /etc/nginx/sites-available/default
```

Add the following in the server {} block with the domain name (replace the existing one if any). It directs the traffic with `/` to the backend fastapi server. Use the port number for the api server.
```
location / {
    proxy_http_version 1.1;
    proxy_cache_bypass $http_upgrade;

    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_connect_timeout 300;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
    send_timeout 300;

    proxy_pass http://127.0.0.1:3000; # React server
}
```

Then start or restart nginx server.
```
sudo systemctl enable nginx
sudo systemctl start nginx
sudo systemctl restart nginx # if the server is running
sudo systemctl reload nginx # if the server does not reflect the new config
sudo systemctl status nginx
```
